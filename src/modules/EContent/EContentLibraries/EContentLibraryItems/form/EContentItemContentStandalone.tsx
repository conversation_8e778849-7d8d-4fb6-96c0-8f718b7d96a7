import React, { useCallback, useMemo } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { useQuery, useMutation } from '@apollo/react-hooks';
import { get } from 'lodash';

import useT from '../../../../../common/components/utils/Translations/useT';
import SpinnerError from '../../../../../common/components/utils/SpinnerError';
import eContentItemContent from '../../../../../common/data/eContent/eContentItemContent.graphql';
import eContentItem from '../../../../../common/data/eContent/eContentItem.graphql';
import updateEContentItemContent from '../../../../../common/data/eContent/updateEContentItemContent.graphql';
import EContentItemContentForm from './tabs/EContentItemContentsTab/form/EContentItemContentForm';
import { handleFormErrors } from '../../../../../common/formHelpers';
import Notifications from '../../../../../common/utils/Notifications';

interface IEContentItemContentStandaloneParams {
  itemId: string;
  contentId: string;
}

export interface IEContentItemContentStandaloneProps {
  libraryId?: number;
}

const EContentItemContentStandalone: React.FC<IEContentItemContentStandaloneProps> = ({
  libraryId,
}) => {
  const t = useT();
  const history = useHistory();
  const {
    itemId,
    contentId,
  } = useParams<IEContentItemContentStandaloneParams>();

  // Fetch the content data
  const {
    data: contentData,
    loading: contentLoading,
    refetch: refetchContent,
  } = useQuery(eContentItemContent, {
    variables: { id: parseInt(contentId) },
    skip: !contentId,
  });

  // Fetch the item data to get resource information
  const { data: itemData, loading: itemLoading } = useQuery(eContentItem, {
    variables: { id: parseInt(itemId) },
    skip: !itemId,
  });

  const [updateContent, { loading: updateLoading }] = useMutation(
    updateEContentItemContent,
  );

  const content = useMemo(() => get(contentData, 'eContentItemContent'), [
    contentData,
  ]);
  const item = useMemo(() => get(itemData, 'eContentItem'), [itemData]);
  const resource = useMemo(() => get(item, 'resource'), [item]);

  const handleSubmit = useCallback(
    async values => {
      try {
        await handleFormErrors(
          updateContent({
            variables: { params: values },
          }),
          t,
        );
        Notifications.success(t('Updated Successfully'), '', t);
        refetchContent();
      } catch (error) {
        // Error is handled by handleFormErrors
      }
    },
    [updateContent, t, refetchContent],
  );

  const handleGoBack = useCallback(() => {
    // Navigate back to the contents list
    if (libraryId && itemId) {
      history.push(
        `/e-content/libraries/edit/${libraryId}/edit/${itemId}/contents`,
      );
    }
  }, [history, libraryId, itemId]);

  const goToContents = useCallback(() => {
    if (libraryId && itemId) {
      history.push(
        `/e-content/libraries/edit/${libraryId}/edit/${itemId}/contents`,
      );
    }
  }, [history, libraryId, itemId]);

  const goToCreate = useCallback(() => {
    if (libraryId && itemId) {
      history.push(
        `/e-content/libraries/edit/${libraryId}/edit/${itemId}/contents/add`,
      );
    }
  }, [history, libraryId, itemId]);

  const goToCreateItem = useCallback(() => {
    if (libraryId) {
      history.push(`/e-content/libraries/edit/${libraryId}/add`);
    }
  }, [history, libraryId]);

  const loading = contentLoading || itemLoading || updateLoading;

  if (!content || !resource) {
    return <SpinnerError loading={loading} />;
  }

  return (
    <SpinnerError loading={loading}>
      <EContentItemContentForm
        entity={content}
        resource={resource}
        onSubmit={handleSubmit}
        onGoBack={handleGoBack}
        goToContents={goToContents}
        goToCreate={goToCreate}
        goToCreateItem={goToCreateItem}
        usedKeywords={item?.usedKeywords}
        floatButton={false}
      />
    </SpinnerError>
  );
};

export default EContentItemContentStandalone;
